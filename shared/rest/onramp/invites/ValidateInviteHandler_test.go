package invites

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks/dbexecutor"
)

// Test_ValidateInviteHandlerWithDeps tests the ValidateInviteHandlerWithDeps function
func Test_ValidateInviteHandlerWithDeps(t *testing.T) {
	t.<PERSON>l()

	userID := "123e4567-e89b-12d3-a456-************"
	validToken := "abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"

	tests := []struct {
		name           string
		method         string
		userID         string
		token          string
		deps           HandlerDeps
		expectedStatus int
	}{
		{
			name:   "success",
			method: "GET",
			userID: userID,
			token:  validToken,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: &dbexecutor.FakeDBExecutor{},
					}, nil
				},
				ValidateInviteToken: func(pg connect.DatabaseExecutor, token string) (*UserInvite, error) {
					userUUID, _ := uuid.Parse(userID)
					return &UserInvite{
						ID:                     uuid.New(),
						OrganizationIdentifier: uuid.New(),
						TokenHash:              "test-hash",
						Email:                  "<EMAIL>",
						InviterID:              userUUID, // Match the user ID
						CustomRoleID:           uuid.New(),
						Status:                 StatusPending,
						Created:                time.Now().UTC(),
						Updated:                time.Now().UTC(),
					}, nil
				},
			},
			expectedStatus: 200,
		},
		{
			name:           "invalid_user_id",
			method:         "GET",
			userID:         "invalid-uuid",
			token:          validToken,
			deps:           setupTestDeps(),
			expectedStatus: 400,
		},
		{
			name:           "missing_token",
			method:         "GET",
			userID:         userID,
			token:          "",
			deps:           setupTestDeps(),
			expectedStatus: 400,
		},
		{
			name:   "invalid_token_format",
			method: "GET",
			userID: userID,
			token:  "invalid-token",
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: &dbexecutor.FakeDBExecutor{},
					}, nil
				},
				ValidateInviteToken: func(pg connect.DatabaseExecutor, token string) (*UserInvite, error) {
					return nil, ErrInvalidInviteToken
				},
			},
			expectedStatus: 401,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			req := httptest.NewRequest(tt.method, "/api/user/"+tt.userID+"/invites/validate?token="+tt.token, nil)
			req = mux.SetURLVars(req, map[string]string{
				"userId": tt.userID,
			})

			w := httptest.NewRecorder()

			handler := ValidateInviteHandlerWithDeps(tt.deps)
			handler(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

// Test_ValidateInviteHandler_Integration tests the ValidateInviteHandler with integration scenarios
func Test_ValidateInviteHandler_Integration(t *testing.T) {
	t.Parallel()

	userID := "123e4567-e89b-12d3-a456-************"
	validToken := "abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890"

	tests := []struct {
		name           string
		method         string
		userID         string
		token          string
		deps           HandlerDeps
		expectedStatus int
	}{
		{
			name:   "successful_invite_validation",
			method: "GET",
			userID: userID,
			token:  validToken,
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: &dbexecutor.FakeDBExecutor{},
					}, nil
				},
				ValidateInviteToken: func(pg connect.DatabaseExecutor, token string) (*UserInvite, error) {
					userUUID, _ := uuid.Parse(userID)
					return &UserInvite{
						ID:                     uuid.New(),
						OrganizationIdentifier: uuid.New(),
						TokenHash:              "test-hash",
						Email:                  "<EMAIL>",
						InviterID:              userUUID, // Match the user ID
						CustomRoleID:           uuid.New(),
						Status:                 StatusPending,
						Created:                time.Now().UTC(),
						Updated:                time.Now().UTC(),
					}, nil
				},
			},
			expectedStatus: 200,
		},
		{
			name:           "invalid_user_id_format",
			method:         "GET",
			userID:         "invalid-uuid-format",
			token:          validToken,
			deps:           setupTestDeps(),
			expectedStatus: 400,
		},
		{
			name:           "missing_token",
			method:         "GET",
			userID:         userID,
			token:          "",
			deps:           setupTestDeps(),
			expectedStatus: 400,
		},
		{
			name:   "invalid_token_format",
			method: "GET",
			userID: userID,
			token:  "invalid-token",
			deps: HandlerDeps{
				GetConnections: func(ctx context.Context, args ...bool) (*connect.Connections, error) {
					return &connect.Connections{
						Postgres: &dbexecutor.FakeDBExecutor{},
					}, nil
				},
				ValidateInviteToken: func(pg connect.DatabaseExecutor, token string) (*UserInvite, error) {
					return nil, ErrInvalidInviteToken
				},
			},
			expectedStatus: 401,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			req := httptest.NewRequest(tt.method, "/api/user/"+tt.userID+"/invites/validate?token="+tt.token, nil)
			req = mux.SetURLVars(req, map[string]string{
				"userId": tt.userID,
			})

			w := httptest.NewRecorder()

			handler := ValidateInviteHandlerWithDeps(tt.deps)
			handler(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

// Test_ValidateInviteHandler_Exported tests that the exported ValidateInviteHandler is properly initialized
func Test_ValidateInviteHandler_Exported(t *testing.T) {
	t.Parallel()

	// Test that the exported handler is properly initialized
	assert.NotNil(t, ValidateInviteHandler)

	// Test that it's a valid http.HandlerFunc
	assert.IsType(t, (*func(http.ResponseWriter, *http.Request))(nil), &ValidateInviteHandler)
}
